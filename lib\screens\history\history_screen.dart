import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:intl/intl.dart';
import 'package:notrail/theme/app_theme.dart'; // Import AppTheme for AppColors
import 'dart:convert'; // For json.decode and json.encode
import 'package:shared_preferences/shared_preferences.dart'; // For SharedPreferences
import 'package:notrail/utils/logger.dart'; // Import logger

class Subscription {
  final String id;
  final String name;
  final DateTime expiryDate;
  final double amount;
  final String category; // Added category
  final int categoryColor; // Added categoryColor (ARGB int)
  bool isCanceled;

  Subscription({
    required this.id,
    required this.name,
    required this.expiryDate,
    required this.amount,
    required this.category,
    required this.categoryColor,
    this.isCanceled = false,
  });

  factory Subscription.fromFirestore(DocumentSnapshot doc) {
    Map data = doc.data() as Map<String, dynamic>;
    return Subscription(
      id: doc.id,
      name: data['name'] ?? '',
      expiryDate: (data['expiryDate'] as Timestamp).toDate(),
      amount: (data['amount'] ?? 0.0).toDouble(),
      category: data['category'] ?? 'Other', // Default to 'Other' if not found
      categoryColor:
          data['categoryColor'] ?? 0xFF9E9E9E, // Default to grey if not found
      isCanceled: data['isCanceled'] ?? false,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'name': name,
      'expiryDate': Timestamp.fromDate(expiryDate),
      'amount': amount,
      'category': category,
      'categoryColor': categoryColor,
      'isCanceled': isCanceled,
    };
  }
}

class HistoryScreen extends StatefulWidget {
  const HistoryScreen({super.key});

  @override
  State<HistoryScreen> createState() => _HistoryScreenState();
}

class _HistoryScreenState extends State<HistoryScreen> {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  User? get currentUser => _auth.currentUser;

  String? _selectedCategoryFilter; // New state variable for category filter
  bool _showCanceledOnly =
      true; // New state variable to toggle between all/canceled
  Map<String, int> _customCategoryColors =
      {}; // Stores custom category colors (category name -> ARGB int)
  List<Map<String, dynamic>> _allAvailableCategories =
      []; // All categories for filtering

  // Predefined categories with icons and colors (mirroring AddSubscriptionScreen)
  final Map<String, Map<String, dynamic>> _predefinedCategoriesMap = {
    'Entertainment': {
      'icon': Icons.movie,
      'color': AppColors.categoryEntertainment,
    },
    'Streaming': {
      'icon': Icons.play_circle_filled,
      'color': AppColors.categoryStreaming,
    },
    'Music': {'icon': Icons.music_note, 'color': AppColors.categoryMusic},
    'Software': {'icon': Icons.computer, 'color': AppColors.categorySoftware},
    'Fitness': {
      'icon': Icons.fitness_center,
      'color': AppColors.categoryFitness,
    },
    'News': {'icon': Icons.newspaper, 'color': AppColors.categoryNews},
    'Finance': {
      'icon': Icons.account_balance,
      'color': AppColors.categoryFinance,
    },
    'Productivity': {
      'icon': Icons.business,
      'color': AppColors.categoryProductivity,
    },
    'Education': {'icon': Icons.school, 'color': AppColors.categoryEducation},
    'Food & Dining': {
      'icon': Icons.restaurant,
      'color': AppColors.categoryFoodDining,
    },
    'Health': {'icon': Icons.local_hospital, 'color': AppColors.categoryHealth},
    'Other': {'icon': Icons.category, 'color': AppColors.categoryOther},
  };

  @override
  void initState() {
    super.initState();
    _loadAllCategories();
  }

  Future<void> _loadAllCategories() async {
    final prefs = await SharedPreferences.getInstance();
    final customColorsJson = prefs.getString('customCategoryColors');
    if (customColorsJson != null) {
      setState(() {
        _customCategoryColors = Map<String, int>.from(
          (json.decode(customColorsJson) as Map).map(
            (key, value) => MapEntry(key, value as int),
          ),
        );
      });
    }

    final List<Map<String, dynamic>> categories = [];
    // Add predefined categories
    _predefinedCategoriesMap.forEach((name, data) {
      categories.add({
        'name': name,
        'color': Color(data['color'].value),
      }); // Use .value for predefined colors
    });
    // Add custom categories
    _customCategoryColors.forEach((name, colorValue) {
      if (!_predefinedCategoriesMap.containsKey(name)) {
        categories.add({'name': name, 'color': Color(colorValue)});
      }
    });
    // Sort categories alphabetically
    categories.sort((a, b) => a['name'].compareTo(b['name']));
    // Add 'All Categories' option at the beginning
    categories.insert(0, {'name': 'All Categories', 'color': Colors.grey});

    setState(() {
      _allAvailableCategories = categories;
    });
  }

  Stream<List<Subscription>> _getSubscriptionStream() {
    if (currentUser == null) {
      return Stream.value([]);
    }
    logger.d(
      'Fetching subscriptions: showCanceledOnly=$_showCanceledOnly, selectedCategoryFilter=$_selectedCategoryFilter',
    );
    Query<Map<String, dynamic>> query = _firestore
        .collection('users')
        .doc(currentUser!.uid)
        .collection('subscriptions');

    if (_showCanceledOnly) {
      query = query.where('isCanceled', isEqualTo: true);
    } else {
      // If not showing only canceled, show all (including non-canceled)
      // No additional .where('isCanceled', isEqualTo: false) needed here
    }

    if (_selectedCategoryFilter != null &&
        _selectedCategoryFilter != 'All Categories') {
      query = query.where('category', isEqualTo: _selectedCategoryFilter);
    }

    return query.snapshots().map((snapshot) {
      return snapshot.docs
          .map((doc) => Subscription.fromFirestore(doc))
          .toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final cardTheme = Theme.of(context).cardTheme;

    return Scaffold(
      backgroundColor: AppColors.primary,
      appBar: AppBar(
        title: Text(
          _showCanceledOnly ? 'Canceled Subscriptions' : 'All Subscriptions',
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        centerTitle: true,
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        shadowColor: cardTheme.shadowColor ?? colorScheme.shadow,
        automaticallyImplyLeading: false,
        
        actions: [
          IconButton(
            icon: Icon(
              _showCanceledOnly ? Icons.filter_alt : Icons.filter_alt_off,
            ),
            onPressed: () {
              setState(() {
                _showCanceledOnly = !_showCanceledOnly;
                _selectedCategoryFilter =
                    null; // Reset filter when toggling view
              });
            },
          ),
        ],
      ),
      body: currentUser == null
          ? _buildSignInPrompt(context)
          : StreamBuilder<List<Subscription>>(
              stream: _getSubscriptionStream(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return Center(
                    child: CircularProgressIndicator(
                      strokeWidth: 3,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        colorScheme.primary,
                      ),
                    ),
                  );
                }
                if (snapshot.hasError) {
                  return _buildErrorView(context, snapshot.error.toString());
                }
                if (!snapshot.hasData || snapshot.data!.isEmpty) {
                  return _buildEmptyState(context);
                }

                final subscriptions = snapshot.data!;
                // Filter subscriptions based on _showCanceledOnly logic for display
                // The Firestore query already handles this, but if you had local filtering,
                // you would apply it here:
                // final filteredSubscriptions = _showCanceledOnly
                //     ? subscriptions.where((sub) => sub.isCanceled).toList()
                //     : subscriptions;

                return RefreshIndicator(
                  onRefresh: () async {
                    setState(() {});
                  },
                  color: colorScheme.primary,
                  child: SingleChildScrollView(
                    physics: const AlwaysScrollableScrollPhysics(),
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Savings Header (only if showing canceled subscriptions)
                        if (_showCanceledOnly) ...[
                          _buildSavingsHeader(context, subscriptions),
                          const SizedBox(height: 24),
                        ],

                        // Category Filter Dropdown
                        DropdownButtonFormField<String>(
                          value: _selectedCategoryFilter ?? 'All Categories',
                          decoration: InputDecoration(
                            labelText: 'Filter by Category',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                color: colorScheme.outlineVariant,
                              ),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                color: colorScheme.outlineVariant,
                              ),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                color: colorScheme.primary,
                                width: 2,
                              ),
                            ),
                            filled: true,
                            fillColor: colorScheme.surfaceContainerHighest.withAlpha((colorScheme.surfaceContainerHighest.alpha * 0.2 * 255.0).round() & 0xff),
                          ),
                          onChanged: (String? newValue) {
                            setState(() {
                              _selectedCategoryFilter =
                                  newValue == 'All Categories'
                                  ? null
                                  : newValue;
                            });
                          },
                          items: _allAvailableCategories
                              .map<DropdownMenuItem<String>>((category) {
                                return DropdownMenuItem<String>(
                                  value: category['name'] as String,
                                  child: Row(
                                    children: [
                                      Icon(
                                        (category['name'] == 'All Categories'
                                            ? Icons.category
                                            : _predefinedCategoriesMap[category['name']]?['icon']) ??
                                            Icons.category,
                                        color: category['color'] as Color,
                                      ),
                                      const SizedBox(width: 10),
                                      Text(category['name'] as String),
                                    ],
                                  ),
                                );
                              })
                              .toList(),
                          isExpanded: true,
                        ),
                        const SizedBox(height: 24),

                        // Subscriptions List
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _showCanceledOnly
                                  ? 'Canceled Subscriptions'
                                  : 'All Subscriptions',
                              style: TextStyle(
                                fontSize: 22,
                                fontWeight: FontWeight.bold,
                                color: colorScheme.onSurface,
                              ),
                            ),
                            const SizedBox(height: 16),
                            // Ensure the list is built from the data fetched by the stream,
                            // which already respects the _showCanceledOnly filter.
                            ...subscriptions.map(
                              (sub) => _buildSubscriptionTile(context, sub),
                            ),
                          ],
                        ),
                        const SizedBox(height: 20),
                      ],
                    ),
                  ),
                );
              },
            ),
    );
  }

  Widget _buildSignInPrompt(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.account_circle_outlined,
            size: 80,
            color: colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: 24),
          Text(
            'Please sign in to continue',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w500,
              color: colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'View your subscriptions history',
            style: TextStyle(fontSize: 14, color: colorScheme.onSurfaceVariant),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorView(BuildContext context, String error) {
    final colorScheme = Theme.of(context).colorScheme;
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 80, color: colorScheme.error),
          const SizedBox(height: 24),
          Text(
            'Something went wrong',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w500,
              color: colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: TextStyle(fontSize: 14, color: colorScheme.onSurfaceVariant),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: colorScheme.primary.withValues(alpha: (colorScheme.primary.alpha * 0.1 * 255.0).round().toDouble()),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.celebration_outlined,
                size: 80,
                color: colorScheme.primary,
              ),
            ),
            const SizedBox(height: 32),
            Text(
              _showCanceledOnly
                  ? 'No canceled subscriptions'
                  : 'No subscriptions found',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              _showCanceledOnly
                  ? 'You haven\'t canceled any subscriptions yet.\nKeep managing your active subscriptions wisely!'
                  : 'Add some subscriptions to see them here!',
              style: TextStyle(
                fontSize: 16,
                color: colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: () {
                logger.d(
                  'Back to Dashboard button pressed',
                ); // Log for debugging
                Navigator.of(context).pop();
              },
              icon: const Icon(Icons.arrow_back),
              label: const Text('Back to Dashboard'),
              style: ElevatedButton.styleFrom(
                backgroundColor: colorScheme.primary,
                foregroundColor: colorScheme.onPrimary,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 16,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSavingsHeader(
    BuildContext context,
    List<Subscription> subscriptions,
  ) {
    final colorScheme = Theme.of(context).colorScheme;
    final double totalMoneySaved = subscriptions
        .where(
          (sub) => sub.isCanceled,
        ) // Only sum canceled subscriptions for "money saved"
        .fold(0.0, (currentSum, sub) => currentSum + sub.amount);
    final int canceledCount = subscriptions
        .where((sub) => sub.isCanceled)
        .length;

    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: colorScheme.primary.withAlpha((colorScheme.primary.alpha * 0.1 * 255.0).round() & 0xff),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Money Saved',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: colorScheme.onPrimary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '\$${totalMoneySaved.toStringAsFixed(2)}',
            style: TextStyle(
              fontSize: 32,
              fontWeight: FontWeight.bold,
              color: colorScheme.onPrimary,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'You have canceled $canceledCount subscriptions.',
            style: TextStyle(
              fontSize: 14,
              color: colorScheme.onPrimary.withAlpha((colorScheme.onPrimary.alpha * 0.7 * 255.0).round() & 0xff),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubscriptionTile(BuildContext context, Subscription subscription) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    subscription.name,
                    style: textTheme.titleLarge!.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onSurface,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Text(
                  '\$${subscription.amount.toStringAsFixed(2)}',
                  style: textTheme.titleMedium!.copyWith(
                    fontWeight: FontWeight.bold,
                    color: colorScheme.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(
                  _predefinedCategoriesMap[subscription.category]?['icon'] ?? Icons.category,
                  color: Color(subscription.categoryColor),
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  subscription.category,
                  style: textTheme.bodyMedium!.copyWith(
                    color: colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Expires on ${DateFormat.yMMMd().format(subscription.expiryDate)}',
              style: textTheme.bodySmall!.copyWith(
                color: colorScheme.onSurfaceVariant,
              ),
            ),
            if (subscription.isCanceled) ...[
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: colorScheme.errorContainer,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  'CANCELED',
                  style: textTheme.labelSmall!.copyWith(
                    color: colorScheme.onErrorContainer,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}